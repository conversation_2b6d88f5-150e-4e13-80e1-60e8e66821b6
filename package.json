{"name": "pianorhythm", "version": "0.10.0", "scripts": {"dev:local": "cross-env NODE_ENV=local-dev vinxi dev --port 80 --host", "dev:dev": "cross-env NODE_ENV=dev vinxi dev --port 80 --host", "build:production": "cross-env NODE_ENV=production vinxi build", "build:staging": "cross-env NODE_ENV=staging BUILD_ENV=staging vinxi build", "tauri:sign-key": "tauri signer generate -w .tauri/pr_v3.key", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:dev2": "tauri dev --no-watch --no-dev-server-wait", "start": "vinxi start", "test": "vitest run", "test-watch": "vitest", "test-ui": "vitest --ui", "test-update-snapshots": "vitest -u", "cy:open": "cypress open", "cy:vite": "cross-env NODE_ENV=local-dev vinxi dev --port 4000 --host", "cy:dev-server": "start-server-and-test cy:vite http://localhost:4000 'cypress open'", "cy:dev-server:tests": "start-server-and-test cy:vite http://localhost:4000 'cypress run'", "cy:run": "cypress run"}, "type": "module", "dependencies": {"@badrap/result": "^0.2.13", "@elastic/react-search-ui-views": "^1.22.2", "@elastic/search-ui": "^1.22.2", "@hope-ui/solid": "github:PianoRhythm/hope-ui#main&path:packages/solid", "@minht11/solid-virtual-container": "^0.2.1", "@msgpack/msgpack": "3.0.0-beta2", "@octokit/rest": "^21.0.2", "@rapideditor/country-coder": "^5.3.0", "@solid-primitives/active-element": "2.0.20", "@solid-primitives/context": "^0.2.3", "@solid-primitives/event-bus": "^1.0.11", "@solid-primitives/local-store": "^1.1.4", "@solid-primitives/map": "0.4.11", "@solid-primitives/media": "^2.2.9", "@solid-primitives/promise": "^1.0.17", "@solid-primitives/resize-observer": "2.0.25", "@solid-primitives/resource": "^0.3.1", "@solid-primitives/storage": "^4.2.1", "@solid-primitives/upload": "0.0.117", "@tauri-apps/api": "2.0.0-rc.4", "@tauri-apps/plugin-dialog": "2.0.0-rc.0", "@tauri-apps/plugin-fs": "2.0.0-rc.2", "@tauri-apps/plugin-http": "2.0.0-rc.0", "@tauri-apps/plugin-log": "2.0.0-rc.0", "@tauri-apps/plugin-notification": "2.0.0-rc.0", "@tauri-apps/plugin-process": "2.0.0-rc.1", "@tauri-apps/plugin-updater": "2.0.0-rc.2", "@thisbeyond/solid-dnd": "^0.7.5", "@thisbeyond/solid-select": "^0.15.0", "abcjs": "^6.4.4", "ackee-tracker": "^5.1.0", "animejs": "^3.2.2", "bowser": "^2.11.0", "clsx": "^2.1.1", "convert-size": "^1.2.1", "countries-list": "^3.1.1", "croppie": "^2.6.5", "i18next": "^23.15.2", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.6.2", "idb-keyval": "^6.2.1", "interactjs": "^1.10.27", "lodash-es": "^4.17.21", "long": "^5.2.3", "luxon": "^3.5.0", "mitt": "^3.0.1", "mousetrap": "^1.6.5", "node-fetch-cache": "^5.0.2", "nouislider": "^15.8.1", "peerjs": "^1.5.4", "protobufjs": "^7.4.0", "rehype-raw": "6.1.1", "rehype-sanitize": "5.0.1", "remark-gfm": "3.0.1", "rxjs": "^7.8.1", "sanitize-html": "^2.13.1", "simple-color-picker": "^1.0.5", "solid-dismiss": "^1.8.2", "solid-icons": "^1.1.0", "solid-immer": "^0.1.1", "solid-markdown": "1.2.2", "solid-motionone": "^1.0.2", "solid-services": "^4.3.2", "solid-toast": "^0.5.0", "solid-transition-group": "^0.3.0", "solidjs-use": "^2.3.0", "sweetalert2": "^11.14.1", "timesync": "^1.0.11", "tinycolor2": "^1.6.0", "ts-pattern": "^5.4.0", "vanilla-cookieconsent": "3.0.1", "web-worker-proxy": "0.5.5", "zipson": "^0.2.12", "zod": "3.23.8"}, "devDependencies": {"@cypress/vite-dev-server": "^6.0.0", "@rollup/plugin-replace": "4.0.0", "@solidjs/meta": "0.29.4", "@solidjs/router": "0.15.3", "@solidjs/start": "1.0.11", "@solidjs/testing-library": "0.8.10", "@tauri-apps/cli": "2.0.0-rc.12", "@testing-library/jest-dom": "^6.5.0", "@testing-library/user-event": "^14.5.2", "@types/animejs": "^3.1.12", "@types/bun": "1.1.10", "@types/howler": "^2.2.12", "@types/lodash-es": "^4.17.12", "@types/mousetrap": "^1.6.15", "@types/node": "^22.7.4", "@types/tinycolor2": "^1.4.6", "@types/webmidi": "^2.1.0", "@vitest/browser": "^2.1.8", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "@vitest/web-worker": "^2.1.8", "bun": "1.1.29", "cross-env": "^7.0.3", "crossws": "^0.2.4", "cypress": "^13.17.0", "cypress-browser-permissions": "^1.1.0", "cypress-vite": "^1.6.0", "fake-indexeddb": "^6.0.0", "internal-ip": "^8.0.0", "jwt-decode": "^4.0.0", "mongodb": "6.13.0", "nitropack": "2.10.4", "postcss": "^8.4.47", "solid-js": "1.9.7", "start-server-and-test": "^2.0.9", "ts-proto": "^1.181.2", "ts-protoc-gen": "^0.15.0", "typescript": "5.7.3", "vinxi": "0.5.1", "vite": "6.1.0", "vite-plugin-run": "0.6.1", "vite-plugin-solid": "2.11.1", "vite-plugin-static-copy": "1.0.6", "vitest": "2.1.8", "vitest-fetch-mock": "^0.4.3", "vitest-matchmedia-mock": "^1.0.6"}, "engines": {"node": ">=18.x"}, "overrides": {"vite": "^5.4.11"}, "pnpm": {"overrides": {"vite": "^5.4.11"}}, "peerDependencies": {"@stitches/core": "^1.2.8"}, "packageManager": "pnpm@9.4.0", "build:date": "1738874491127"}