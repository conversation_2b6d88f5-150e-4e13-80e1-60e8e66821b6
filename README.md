# PianoRhythm

PianoRhythm is a multiplayer social web application that allows users to play music and casual games with people from around the world. Experience real-time musical collaboration with advanced 3D rendering, custom synthesizers, and seamless cross-platform support.

🎹 **[Try PianoRhythm Live](https://pianorhythm.github.io/pianorhythm-ssr/)** | 🌐 **[Main Site](https://pianorhythm.io)** | 📚 **[Documentation](https://docs.pianorhythm.io)**

## About the Project

PianoRhythm is designed to create a collaborative musical experience where users can:

- **Play music together in real-time** with low-latency synchronization
- **Interact through chat and social features** with typing indicators and user profiles
- **Join or create themed rooms** with customizable environments
- **Participate in casual games** including rhythm games and musical challenges
- **Customize their experience** with multiple themes, 3D/2D rendering modes, and UI preferences
- **Use MIDI devices** for authentic piano playing experience

## Technical Architecture

### Frontend
- **Framework**: SolidJS with Server-Side Rendering (SSR)
- **Language**: TypeScript
- **Real-time Communication**: WebSockets, WebRTC
- **UI Components**: Hope UI (custom fork)
- **State Management**: Solid Services, Immer
- **Build System**: Vinxi with custom presets
- **Testing**: Vitest, Solid Testing Library, Cypress

### Backend
- **Runtime**: Bun (production), Node.js (development)
- **Real-time Server**: WebSocket with crossws adapter
- **Database**: MongoDB with Data API
- **API**: REST endpoints with server middleware
- **Authentication**: OAuth (Discord, GitHub) with JWT

### Core Engine
- **Language**: Rust (Nightly toolchain)
- **Integration**: WebAssembly with wasm-bindgen
- **Serialization**: Protocol Buffers
- **Audio**: Custom synthesizer implementation with Web Audio API
- **3D Rendering**: Bevy Engine 0.16 (WebGPU/WebGL2)
- **MIDI Support**: Web MIDI API integration

### Cross-Platform Support
- **Web Application**: Primary platform with GitHub Pages deployment
- **Desktop Application**: Tauri-based native wrapper
- **Environment Detection**: Adaptive features based on platform
- **Mobile Support**: Responsive design with touch controls

## Key Features

### Music Collaboration
- **Real-time MIDI synchronization** with sub-100ms latency
- **Advanced synthesizer** with custom Rust-based audio engine
- **Multi-instrument support** with realistic piano physics
- **Sheet music integration** with interactive score display
- **Recording and playback** capabilities

### Social Interaction
- **Room-based collaboration** with up to 16 simultaneous players
- **Real-time chat** with typing indicators and emoji support
- **User profiles** with customizable avatars and statistics
- **Friend system** with online status and invitations
- **Admin dashboard** for room management

### User Experience
- **Multiple themes** with dark/light mode support
- **Internationalization (i18n)** with multi-language support
- **3D and 2D rendering modes** powered by Bevy Engine
- **Responsive design** optimized for desktop and mobile
- **Accessibility features** with keyboard navigation

### Technical Features
- **Comprehensive error handling** with user-friendly feedback
- **Automated testing** with 90%+ code coverage
- **CI/CD pipeline** with self-hosted runners
- **Performance monitoring** with real-time metrics

## Deployment

### GitHub Pages (Current)
The project is automatically deployed to GitHub Pages on every push to the main branch:
- **Live Demo**: https://pianorhythm.github.io/pianorhythm-ssr/
- **Deployment**: Automatic via GitHub Actions
- **Build**: Static site generation with SSR prerendering

### Production Deployment
For production environments, the application is deployed using:
- **Container Registry**: DigitalOcean Container Registry
- **Runtime**: Bun server with Docker containers
- **CDN**: Custom assets and static content delivery
- **Database**: MongoDB Atlas with Data API

## Development

### Prerequisites
- Node.js 19.2.0+
- pnpm (recommended package manager)
- Rust nightly toolchain (for core development)
- wasm-bindgen CLI

### Getting Started

1. **Clone the repository**:
```bash
git clone https://github.com/PianoRhythm/pianorhythm-ssr.git
cd pianorhythm-ssr
```

2. **Install dependencies**:
```bash
pnpm install
```

3. **Start development server**:
```bash
pnpm run dev:local
```

The application will be available at `http://localhost` (port 80).

### Development Scripts

```bash
# Development servers
pnpm run dev:local    # Local development with hot reload
pnpm run dev:dev      # Development environment

# Building
pnpm run build:production  # Production build
pnpm run build:staging     # Staging build

# Testing
pnpm test             # Run unit tests
pnpm test-watch       # Watch mode for tests
pnpm test-ui          # Visual test interface
pnpm cy:open          # Open Cypress for e2e tests

# Desktop app (Tauri)
pnpm run tauri:dev    # Desktop development
pnpm run tauri        # Tauri CLI commands
```

## Building

The project uses **Vinxi** with custom presets for different deployment environments:

- **Development**: Hot reload with source maps
- **Staging**: Optimized build with debugging enabled
- **Production**: Fully optimized with prerendering and minification

### Core Engine Building

The Rust core engine requires special build steps:

```bash
# Build WebAssembly core
cd pianorhythm_core
chmod +x ./build-core-release.sh
./build-core-release.sh

# Build 3D renderer
chmod +x ./build-bevy-renderer-wasm-webgpu.sh
./build-bevy-renderer-wasm-webgpu.sh
```

## Testing

Comprehensive testing setup with multiple frameworks:

### Unit Tests
```bash
pnpm test                    # Run all unit tests
pnpm test-watch              # Watch mode
pnpm test-update-snapshots   # Update test snapshots
```

### Integration Tests
```bash
pnpm cy:open                 # Open Cypress test runner
pnpm cy:vite                 # Start test server
```

### Test Coverage
- **Frontend**: Vitest with Solid Testing Library
- **Components**: Snapshot and interaction testing
- **Services**: Mock-based unit testing
- **E2E**: Cypress for full user workflows

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes and add tests
4. Ensure all tests pass (`pnpm test`)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

### Development Guidelines
- Follow TypeScript strict mode
- Write tests for new features
- Use conventional commit messages
- Ensure Rust code passes `cargo clippy`
- Test across different browsers and devices

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Built with [SolidJS](https://solidjs.com/) and [Vinxi](https://vinxi.vercel.app/)
- 3D rendering powered by [Bevy Engine](https://bevyengine.org/)
- Audio synthesis with custom Rust implementation
- UI components based on [Hope UI](https://hope-ui.com/)

---

**PianoRhythm** - Bringing people together through music 🎹✨
